<?php
/**
 * Prosty test dla funkcjonalności PriceCalendar z nowymi polami dat
 */

require_once 'config.php';
require_once 'application/_engine_v4_0/libs/Models/PriceCalendar.php';

use App\Libs\Models\PriceCalendar;

echo "<h1>Test funkcjonalności PriceCalendar z nowymi polami dat</h1>\n";

// Test 1: Sprawdzenie czy nowe pola są poprawnie zapisywane w konfiguracji
echo "<h2>Test 1: Zapisywanie konfiguracji z nowymi polami dat</h2>\n";

$pc = new PriceCalendar();

$testConfig = [
    'pass_date_type' => 'weekdays',
    'pass_weekdays_min_date' => '2024-01-01',
    'pass_weekdays_max_date' => '2024-01-31',
    'pass_weekdays' => [
        'monday' => [
            'enabled' => true,
            'time_from' => '09:00',
            'time_to' => '17:00'
        ],
        'wednesday' => [
            'enabled' => true,
            'time_from' => '10:00',
            'time_to' => '16:00'
        ],
        'friday' => [
            'enabled' => true,
            'time_from' => '08:00',
            'time_to' => '18:00'
        ]
    ]
];

$pc->setConfiguration($testConfig);
$savedConfig = $pc->getConfiguration();

echo "Konfiguracja została zapisana:\n";
echo "<pre>" . print_r($savedConfig, true) . "</pre>\n";

// Sprawdzenie czy nowe pola są obecne
if (isset($savedConfig['pass_weekdays_min_date']) && isset($savedConfig['pass_weekdays_max_date'])) {
    echo "<span style='color: green;'>✓ Nowe pola dat zostały poprawnie zapisane</span>\n";
} else {
    echo "<span style='color: red;'>✗ Nowe pola dat nie zostały zapisane</span>\n";
}

echo "<br><br>\n";

// Test 2: Sprawdzenie logiki w generateWeekdaysCalendar
echo "<h2>Test 2: Logika generateWeekdaysCalendar</h2>\n";

// Symulacja metody generateWeekdaysCalendar (bez rzeczywistego zapisu do bazy)
function testGenerateWeekdaysCalendarLogic($config, $sellDateFrom, $sellDateTo) {
    $weekdays = $config['pass_weekdays'] ?? [];
    if (empty($weekdays)) {
        return ['error' => 'Brak konfiguracji dni tygodnia'];
    }

    // Sprawdź czy są ustawione daty w konfiguracji
    $minDate = $config['pass_weekdays_min_date'] ?? null;
    $maxDate = $config['pass_weekdays_max_date'] ?? null;

    // Jeśli są ustawione daty w konfiguracji, użyj ich, w przeciwnym razie użyj sell_date_from/sell_date_to
    if (!empty($minDate) && !empty($maxDate)) {
        $dateFrom = new DateTime($minDate);
        $dateTo = new DateTime($maxDate);
        $dateSource = 'configuration';
    } else {
        $dateFrom = new DateTime($sellDateFrom);
        $dateTo = new DateTime($sellDateTo);
        $dateSource = 'sell_dates';
    }

    $current = clone $dateFrom;
    $generatedDates = [];

    while ($current <= $dateTo) {
        $dayName = strtolower($current->format('l'));

        if (isset($weekdays[$dayName])) {
            $dayConfig = $weekdays[$dayName];
            $enabled = ($dayConfig['enabled'] == 1 || $dayConfig['enabled'] === true);

            if ($enabled && isset($dayConfig['time_from']) && isset($dayConfig['time_to'])) {
                $timeFrom = $dayConfig['time_from'];
                $timeTo = $dayConfig['time_to'];

                $datetimeStart = $current->format('Y-m-d') . ' ' . $timeFrom . ':00';
                $datetimeStop = $current->format('Y-m-d') . ' ' . $timeTo . ':00';

                $generatedDates[] = [
                    'date' => $current->format('Y-m-d'),
                    'day' => $dayName,
                    'start' => $datetimeStart,
                    'stop' => $datetimeStop
                ];
            }
        }

        $current->add(new DateInterval('P1D'));
    }

    return [
        'date_source' => $dateSource,
        'date_from' => $dateFrom->format('Y-m-d'),
        'date_to' => $dateTo->format('Y-m-d'),
        'generated_count' => count($generatedDates),
        'generated_dates' => $generatedDates
    ];
}

// Test z nowymi datami z konfiguracji
$result1 = testGenerateWeekdaysCalendarLogic($testConfig, '2024-02-01', '2024-02-29');

echo "Test z datami z konfiguracji:\n";
echo "<pre>" . print_r($result1, true) . "</pre>\n";

if ($result1['date_source'] === 'configuration') {
    echo "<span style='color: green;'>✓ Używa dat z konfiguracji</span>\n";
} else {
    echo "<span style='color: red;'>✗ Nie używa dat z konfiguracji</span>\n";
}

echo "<br><br>\n";

// Test bez nowych dat w konfiguracji (fallback do sell_dates)
$testConfigWithoutDates = $testConfig;
unset($testConfigWithoutDates['pass_weekdays_min_date']);
unset($testConfigWithoutDates['pass_weekdays_max_date']);

$result2 = testGenerateWeekdaysCalendarLogic($testConfigWithoutDates, '2024-02-01', '2024-02-29');

echo "Test bez dat w konfiguracji (fallback):\n";
echo "<pre>" . print_r($result2, true) . "</pre>\n";

if ($result2['date_source'] === 'sell_dates') {
    echo "<span style='color: green;'>✓ Poprawnie używa sell_dates jako fallback</span>\n";
} else {
    echo "<span style='color: red;'>✗ Nie używa sell_dates jako fallback</span>\n";
}

echo "<br><br>\n";

// Test 3: Sprawdzenie czy generowane są odpowiednie dni
echo "<h2>Test 3: Sprawdzenie generowanych dni</h2>\n";

$expectedDays = ['monday', 'wednesday', 'friday'];
$generatedDays = array_unique(array_column($result1['generated_dates'], 'day'));

echo "Oczekiwane dni: " . implode(', ', $expectedDays) . "\n<br>";
echo "Wygenerowane dni: " . implode(', ', $generatedDays) . "\n<br>";

$correctDays = array_diff($expectedDays, $generatedDays) === [] && array_diff($generatedDays, $expectedDays) === [];

if ($correctDays) {
    echo "<span style='color: green;'>✓ Wygenerowane zostały poprawne dni tygodnia</span>\n";
} else {
    echo "<span style='color: red;'>✗ Wygenerowane dni nie są poprawne</span>\n";
}

echo "<br><br>\n";

echo "<h2>Podsumowanie testów</h2>\n";
echo "Wszystkie testy zostały wykonane. Sprawdź wyniki powyżej.\n";

?>
