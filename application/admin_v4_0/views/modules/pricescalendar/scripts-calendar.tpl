<script>
    $(document).ready(function () {

      console.log('scripts loaded');

      $('#typ-produktu').on('change',function() {
        var sel = $(this).val();
        console.log(sel);
        if(!sel) return;
        var oferta = $('#oferta').val();
        if(oferta === 'all') return false;
        $.get('ajadmin.php?pricescalendar&a=opl&offerid='+oferta+'&ptype='+sel,function(resp){
          console.log(resp);
          if(resp.error){
            alert(resp.msg);
            $('select#produkt').html('').trigger('chosen:updated');
            return false;
          }
          var opt = "<option value=''>{{App::_Lang('wybierz typ produktu')}}</option>";

          $.each(resp.product,function(idx,el){
            opt += '<option value="'+ el.id+'">'+el.nazwa+'</option>';
          });
          $('select#produkt').html(opt).trigger('chosen:updated');
        },'json');
        console.log($('#przeznaczenie-kuponu').val());
      });

      $('#produkt').on('change',function() {
        var produkt = $(this).val();
        console.log(produkt);
        if(!produkt) {
          return
        }
        var oferta = $('#oferta').val();
        var ptype = $('#typ-produktu').val();
        $.get('ajadmin.php?pricescalendar&a=pvl&offerid='+oferta+'&ptype='+ptype+'&product='+produkt,function(resp){
          console.log(resp);
          if(resp.error){
            alert(resp.msg);
            $('select#produkt').html('').trigger('chosen:updated');
            return false;
          }
          var opt = "<option value=''>{{App::_Lang('wybierz wariant produktu')}}</option>";

          $.each(resp.variants,function(idx, el){
            opt += '<option value="'+ el.bsid+'" data-price="'+el.cena+'">'+el.nazwa+'</option>';
          });
          $('select#wariant').html(opt).trigger('chosen:updated');
        },'json');
      });

      $('#wariant').on('change',function() {
        let price = $('option:selected', this).first().attr('data-price') ?? 0;
        let targetField = $('#base_price');
        if(!price) {
          $(targetField).html('0');
        } else {
          $(targetField).html(price);
        }
      });

      $('#oferta').on('change',function(){
        $('#typ-produktu').change();
      });

        $('input#ograniczone').click(function () {
            if ($(this).is(":checked")) {
                $('#ilosc_kart').prop('disabled', false);
            } else
                $('#ilosc_kart').prop('disabled', true);
        });



        $('form#edit').on('submit', function () {
            if (required()) {
                // Sprawdź czy pole 'active' jest zaznaczone i czy są skonfigurowane daty
                if ($('#active').is(':checked') && !hasConfiguredDates()) {
                    alert("{{App::_Lang('Nie można aktywować kalendarza bez skonfigurowanych dat karnetu','Promocje')}}");
                    event.preventDefault();
                    return false;
                }
                return true;
            } else {
                alert("{{App::_Lang('Proszę uzupełnić pola','Promocje')}}");
                event.preventDefault();
                return false;
            }
        });

        $('#nieograniczone').on('change', function () {
            if ($(this).prop('checked')) {
                console.log('checked');
                $('#wielkosc-emisji-row,#wartosc-kuponow-row').hide();
              $('#volumen').prop('required', false);
            }
            else {
                console.log('unchecked');
                $('#wielkosc-emisji-row,#wartosc-kuponow-row').show();
                if($('#emisjakuponu').val() !== 'OBCA') {
                   $('#volumen').prop('required', true);
                }
            }
        });


        $("#dataod").daterangepicker({
            singleDatePicker: true,
            timePicker: true,
            timePicker12Hour: false,
            format: 'YYYY-MM-DD HH:mm',
            startDate: moment().hours(0).seconds(0).minutes(0).format('YYYY-MM-DD HH:mm'),
            minDate: moment().hours(0).seconds(0).minutes(0).format('YYYY-MM-DD HH:mm'),
            timePickerIncrement: 1
        });

        $("#datado").daterangepicker({
            singleDatePicker: true,
            format: 'YYYY-MM-DD HH:mm',
            timePicker: true,
            timePicker12Hour: false,
            startDate: moment().hours(0).seconds(0).minutes(0).format('YYYY-MM-DD HH:mm'),
            minDate: moment().hours(0).seconds(0).minutes(0).format('YYYY-MM-DD HH:mm'),
            timePickerIncrement: 1
        });

        $("#dataod").on('apply.daterangepicker', function (ev, picker) {
            $("#datado").data('daterangepicker').setOptions({
                singleDatePicker: true,
                format: 'YYYY-MM-DD HH:mm',
                timePicker: true,
                timePicker12Hour: false,
                startDate: picker.startDate.format('YYYY-MM-DD HH:mm'),
                minDate: picker.startDate.format('YYYY-MM-DD HH:mm'),
                timePickerIncrement: 1
            });
        });
        
        $('#emitent').change();

        initPassConfiguration();

        // Walidacja pola 'active' na bieżąco
        $('#active').on('change', function() {
            if ($(this).is(':checked') && !hasConfiguredDates()) {
                alert("{{App::_Lang('Nie można aktywować kalendarza bez skonfigurowanych dat karnetu','Promocje')}}");
                $(this).prop('checked', false);
            }
        });

        $('#save_and_generate').on('click', function() {
            var calendarId = $(this).data('id');
            if (!calendarId) {
                alert('{{App::_Lang("Brak ID kalendarza")}}');
                return;
            }

            if (confirm('{{App::_Lang("Czy chcesz zapisać i wygenerować kalendarz cen?")}}')) {
                // First save the form
                var form = $('#edit');
                var formData = form.serialize();

                $.post(form.attr('action'), formData, function(response) {
                    window.location.href = 'admin.php?pricescalendar&a=generate&id=' + calendarId;
                }).fail(function() {
                    alert('{{App::_Lang("Błąd podczas zapisywania")}}');
                });
            }
        });

    });

    // Pass Configuration Functions
    function initPassConfiguration() {
        $('input[name="configuration[pass_date_type]"]').on('change', function() {
            var selectedType = $(this).val();
            if (selectedType === 'specific_dates') {
                $('#specific-dates-section').show();
                $('#weekdays-section').hide();
                $('#specific-dates-section input').prop('disabled', false);
                $('#weekdays-section input').prop('disabled', true);
            } else if (selectedType === 'weekdays') {
                $('#specific-dates-section').hide();
                $('#weekdays-section').show();
                $('#specific-dates-section input').prop('disabled', true);
                $('#weekdays-section input').prop('disabled', false);
            }

            // Sprawdź czy pole 'active' powinno zostać wyłączone po zmianie typu
            checkActiveFieldAfterDateChange();
        });

        // Walidacja dat dla sekcji weekdays
        $('input[name="configuration[pass_weekdays_min_date]"], input[name="configuration[pass_weekdays_max_date]"]').on('change', function() {
            validateWeekdaysDateRange();
        });

        // Inicjalna walidacja dat
        validateWeekdaysDateRange();

        // Add new specific date row
        $('#add-specific-date').on('click', function() {
            var container = $('#specific-dates-container');
            var currentRows = container.find('.specific-date-row').length;
            var newIndex = currentRows;

            var newRow = $('<div class="specific-date-row" data-index="' + newIndex + '">' +
                '<div class="row mb-10">' +
                    '<div class="col-md-4">' +
                        '<div class="input-group">' +
                            '<span class="input-group-addon">{{App::_Lang("Data")}}</span>' +
                            '<input type="date" class="form-control specific-date-input" name="configuration[pass_specific_dates][' + newIndex + '][date]" required>' +
                        '</div>' +
                    '</div>' +
                    '<div class="col-md-3">' +
                        '<div class="input-group">' +
                            '<span class="input-group-addon">{{App::_Lang("Od")}}</span>' +
                            '<input type="time" class="form-control" name="configuration[pass_specific_dates][' + newIndex + '][time_from]" value="09:00" required>' +
                        '</div>' +
                    '</div>' +
                    '<div class="col-md-3">' +
                        '<div class="input-group">' +
                            '<span class="input-group-addon">{{App::_Lang("Do")}}</span>' +
                            '<input type="time" class="form-control" name="configuration[pass_specific_dates][' + newIndex + '][time_to]" value="17:00" required>' +
                        '</div>' +
                    '</div>' +
                    '<div class="col-md-2">' +
                        '<button type="button" class="btn btn-danger btn-sm remove-specific-date">' +
                            '<i class="fa fa-minus"></i> {{App::_Lang("Usuń")}}' +
                        '</button>' +
                    '</div>' +
                '</div>' +
            '</div>');

            container.append(newRow);
            updateRemoveButtons();

            // Dodaj obsługę zmiany dla nowych pól
            newRow.find('input').on('change', function() {
                checkActiveFieldAfterDateChange();
            });
        });

        // Remove specific date row
        $(document).on('click', '.remove-specific-date', function() {
            $(this).closest('.specific-date-row').remove();
            reindexSpecificDates();
            updateRemoveButtons();
            checkActiveFieldAfterDateChange();
        });

        // Monitor changes in specific date inputs
        $(document).on('change', '.specific-date-input, input[name*="[time_from]"], input[name*="[time_to]"]', function() {
            checkActiveFieldAfterDateChange();
        });

        // Handle weekday checkbox changes
        $(document).on('change', '.weekday-checkbox', function() {
            var isChecked = $(this).is(':checked');
            var weekdayRow = $(this).closest('.weekday-row');
            var timeInputs = weekdayRow.find('.weekday-times');

            if (isChecked) {
                timeInputs.show();
                timeInputs.find('input').prop('required', true);
            } else {
                timeInputs.hide();
                timeInputs.find('input').prop('required', false);
            }

            // Sprawdź czy pole 'active' powinno zostać wyłączone
            checkActiveFieldAfterDateChange();
        });

        $('input[name="pass_date_type"]:checked').trigger('change');
    }

    function updateRemoveButtons() {
        var rows = $('#specific-dates-container .specific-date-row');
        rows.each(function(index) {
            var removeBtn = $(this).find('.remove-specific-date');
            if (rows.length <= 1) {
                removeBtn.hide();
            } else {
                removeBtn.show();
            }
        });
    }

    /**
     * Sprawdza czy są skonfigurowane daty karnetu
     * @returns {boolean}
     */
    function hasConfiguredDates() {
        var dateType = $('input[name="configuration[pass_date_type]"]:checked').val();

        if (dateType === 'specific_dates') {
            // Sprawdź czy jest przynajmniej jedna konkretna data z wypełnionymi polami
            var hasValidDate = false;
            $('#specific-dates-container .specific-date-row').each(function() {
                var date = $(this).find('input[name*="[date]"]').val();
                var timeFrom = $(this).find('input[name*="[time_from]"]').val();
                var timeTo = $(this).find('input[name*="[time_to]"]').val();

                if (date && timeFrom && timeTo) {
                    hasValidDate = true;
                    return false; // break z each
                }
            });
            return hasValidDate;
        } else if (dateType === 'weekdays') {
            // Sprawdź czy jest przynajmniej jeden dzień tygodnia zaznaczony
            var hasEnabledWeekday = false;
            $('.weekday-checkbox:checked').each(function() {
                var weekdayRow = $(this).closest('.weekday-row');
                var timeFrom = weekdayRow.find('input[name*="[time_from]"]').val();
                var timeTo = weekdayRow.find('input[name*="[time_to]"]').val();

                if (timeFrom && timeTo) {
                    hasEnabledWeekday = true;
                    return false; // break z each
                }
            });
            return hasEnabledWeekday;
        }

        return false;
    }

    function reindexSpecificDates() {
        $('#specific-dates-container .specific-date-row').each(function(index) {
            $(this).attr('data-index', index);
            $(this).find('input[name*="pass_specific_dates"]').each(function() {
                var name = $(this).attr('name');
                var newName = name.replace(/\[\d+\]/, '[' + index + ']');
                $(this).attr('name', newName);
            });
        });
    }

    /**
     * Waliduje zakres dat dla sekcji weekdays
     */
    function validateWeekdaysDateRange() {
        var minDate = $('input[name="configuration[pass_weekdays_min_date]"]').val();
        var maxDate = $('input[name="configuration[pass_weekdays_max_date]"]').val();

        if (minDate && maxDate) {
            var minDateObj = new Date(minDate);
            var maxDateObj = new Date(maxDate);

            if (minDateObj > maxDateObj) {
                $('input[name="configuration[pass_weekdays_max_date]"]').val('');
                alert("{{App::_Lang('Data maksymalna nie może być wcześniejsza niż data minimalna')}}");
            }
        }
    }

    /**
     * Sprawdza czy pole 'active' powinno zostać wyłączone po zmianie konfiguracji dat
     */
    function checkActiveFieldAfterDateChange() {
        if ($('#active').is(':checked') && !hasConfiguredDates()) {
            $('#active').prop('checked', false);
            alert("{{App::_Lang('Pole aktywności zostało wyłączone, ponieważ nie ma skonfigurowanych dat karnetu','Promocje')}}");
        }
    }
</script>